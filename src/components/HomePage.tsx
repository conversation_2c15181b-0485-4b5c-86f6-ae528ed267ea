'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import GithubPrInput from './GithubPrInput';
import { ParsedPrUrl } from '../types';
import { GithubLogoIcon, WandIcon } from './Icons';

const HomePage: React.FC = () => {
  const router = useRouter();

  const handlePrSubmit = (prData: ParsedPrUrl) => {
    console.log('PR submitted:', prData);
    router.push(`/gh/${prData.org}/${prData.repo}/${prData.pr_num}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-850 to-slate-900 text-white flex flex-col items-center justify-center p-4 sm:p-6 selection:bg-purple-500 selection:text-white overflow-hidden">
      {/* Subtle animated background elements */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden z-0 pointer-events-none">
        <div className="absolute -top-1/4 -left-1/4 w-1/2 h-1/2 bg-purple-600/10 rounded-full filter blur-3xl animate-pulse-slow"></div>
        <div className="absolute -bottom-1/4 -right-1/4 w-1/2 h-1/2 bg-pink-500/10 rounded-full filter blur-3xl animate-pulse-slower"></div>
      </div>
      
      <main className="w-full max-w-xl z-10 flex flex-col items-center">
        <header className="text-center mb-10 sm:mb-12">
          <div className="flex justify-center items-center mb-4">
            <WandIcon className="w-12 h-12 sm:w-16 sm:h-16 text-purple-400 animate-float" />
          </div>
          <h1 className="text-5xl sm:text-6xl md:text-7xl font-extrabold mb-3 tracking-tight">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-400 via-pink-500 to-red-500">
              Glide
            </span>
          </h1>
          <p className="text-lg sm:text-xl text-slate-300 max-w-md mx-auto">
            Understand code changes effortlessly. Paste a PR link to get started.
          </p>
        </header>

        <GithubPrInput onSubmit={handlePrSubmit} />

        <div className="mt-10 text-center text-slate-400">
          <p className="text-sm flex items-center justify-center">
            <GithubLogoIcon className="w-4 h-4 mr-1.5 text-slate-500" />
            Analyzes public GitHub Pull Requests.
          </p>
        </div>
      </main>

      <footer className="absolute bottom-6 sm:bottom-8 text-center text-slate-500 text-xs sm:text-sm z-10">
        <p>&copy; {new Date().getFullYear()} <a href="https://useglide.ai" target="_blank" rel="noopener noreferrer" className="hover:text-purple-400 transition-colors">
          useglide.ai
        </a></p>
      </footer>

      <style jsx>{`
        @keyframes float {
          0% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
          100% { transform: translateY(0px); }
        }
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
        @keyframes pulse-slow {
          0%, 100% { opacity: 1; transform: scale(1); }
          50% { opacity: 0.6; transform: scale(1.05); }
        }
        .animate-pulse-slow {
          animation: pulse-slow 8s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        @keyframes pulse-slower {
          0%, 100% { opacity: 0.8; transform: scale(1); }
          50% { opacity: 0.4; transform: scale(1.1); }
        }
        .animate-pulse-slower {
          animation: pulse-slower 10s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        .bg-slate-850 { background-color: #171E2B; } /* Custom intermediate color */
      `}</style>
    </div>
  );
};

export default HomePage;
